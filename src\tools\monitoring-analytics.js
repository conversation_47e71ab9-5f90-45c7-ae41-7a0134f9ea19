const si = require('systeminformation');
const fs = require('fs-extra');
const path = require('path');
const os = require('os');
const Logger = require('../utils/logger');
const { ToolError } = require('../utils/error-handler');

class MonitoringAnalytics {
  constructor() {
    this.name = 'monitoring-analytics';
    this.description = 'System monitoring, performance analytics, and health checks';
    this.category = 'monitoring';
    this.logger = new Logger('MonitoringAnalytics');
    
    // Performance tracking
    this.executionCount = 0;
    this.lastUsed = null;
    this.averageExecutionTime = 0;
    
    // Monitoring data storage
    this.metricsHistory = [];
    this.alertThresholds = {
      cpu: 80,
      memory: 85,
      disk: 90,
      temperature: 70
    };

    this.schema = {
      type: 'object',
      properties: {
        operation: {
          type: 'string',
          enum: [
            'real_time_metrics',
            'performance_analysis',
            'health_check',
            'resource_usage',
            'process_monitor',
            'disk_analysis',
            'network_monitor',
            'system_alerts',
            'benchmark',
            'trend_analysis'
          ],
          description: 'Type of monitoring operation to perform'
        },
        duration: {
          type: 'number',
          description: 'Duration for monitoring in seconds',
          default: 60
        },
        interval: {
          type: 'number',
          description: 'Sampling interval in seconds',
          default: 5
        },
        processName: {
          type: 'string',
          description: 'Process name to monitor'
        },
        alertThresholds: {
          type: 'object',
          description: 'Custom alert thresholds'
        },
        outputFormat: {
          type: 'string',
          enum: ['json', 'csv', 'summary'],
          description: 'Output format for results',
          default: 'json'
        }
      },
      required: ['operation']
    };

    this.examples = [
      {
        description: 'Get real-time system metrics',
        parameters: { operation: 'real_time_metrics', duration: 30, interval: 5 }
      },
      {
        description: 'Perform system health check',
        parameters: { operation: 'health_check' }
      },
      {
        description: 'Monitor specific process',
        parameters: { operation: 'process_monitor', processName: 'node', duration: 60 }
      }
    ];

    this.keywords = ['monitor', 'performance', 'metrics', 'analytics', 'health', 'system', 'resources'];
  }

  async execute(parameters) {
    this.executionCount++;
    this.lastUsed = new Date().toISOString();
    
    const startTime = Date.now();
    
    try {
      const { operation } = parameters;
      let result;
      
      switch (operation) {
        case 'real_time_metrics':
          result = await this.realTimeMetrics(parameters);
          break;
        case 'performance_analysis':
          result = await this.performanceAnalysis(parameters);
          break;
        case 'health_check':
          result = await this.systemHealthCheck(parameters);
          break;
        case 'resource_usage':
          result = await this.resourceUsage(parameters);
          break;
        case 'process_monitor':
          result = await this.processMonitor(parameters);
          break;
        case 'disk_analysis':
          result = await this.diskAnalysis(parameters);
          break;
        case 'network_monitor':
          result = await this.networkMonitor(parameters);
          break;
        case 'system_alerts':
          result = await this.systemAlerts(parameters);
          break;
        case 'benchmark':
          result = await this.benchmark(parameters);
          break;
        case 'trend_analysis':
          result = await this.trendAnalysis(parameters);
          break;
        default:
          throw new ToolError(`Unknown monitoring operation: ${operation}`);
      }
      
      const executionTime = Date.now() - startTime;
      this.updateExecutionStats(executionTime);
      
      return {
        success: true,
        operation,
        result,
        executionTime,
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      this.logger.error('Monitoring operation failed:', error);
      throw new ToolError(`Monitoring operation failed: ${error.message}`, error);
    }
  }

  async realTimeMetrics(parameters) {
    const { duration = 60, interval = 5 } = parameters;
    const metrics = [];
    const startTime = Date.now();
    
    while (Date.now() - startTime < duration * 1000) {
      const timestamp = new Date().toISOString();
      
      const [cpu, memory, load, temp] = await Promise.all([
        si.currentLoad(),
        si.mem(),
        si.currentLoad(),
        si.cpuTemperature().catch(() => ({ main: null }))
      ]);

      const metric = {
        timestamp,
        cpu: {
          usage: parseFloat(cpu.currentLoad.toFixed(2)),
          user: parseFloat(cpu.currentLoadUser.toFixed(2)),
          system: parseFloat(cpu.currentLoadSystem.toFixed(2))
        },
        memory: {
          total: memory.total,
          used: memory.used,
          free: memory.free,
          usage: parseFloat(((memory.used / memory.total) * 100).toFixed(2))
        },
        load: {
          avg1: load.avgLoad,
          avg5: load.avgLoad,
          avg15: load.avgLoad
        },
        temperature: temp.main
      };

      metrics.push(metric);
      this.metricsHistory.push(metric);
      
      // Keep history manageable
      if (this.metricsHistory.length > 1000) {
        this.metricsHistory = this.metricsHistory.slice(-500);
      }
      
      if (Date.now() - startTime < duration * 1000) {
        await this.delay(interval * 1000);
      }
    }

    return {
      duration,
      interval,
      samplesCollected: metrics.length,
      metrics,
      summary: this.generateMetricsSummary(metrics)
    };
  }

  async performanceAnalysis(parameters) {
    const [cpu, memory, disk, network, processes] = await Promise.all([
      si.cpu(),
      si.mem(),
      si.fsSize(),
      si.networkStats(),
      si.processes()
    ]);

    const analysis = {
      cpu: {
        model: cpu.brand,
        cores: cpu.cores,
        speed: cpu.speed,
        cache: cpu.cache,
        performance: await this.analyzeCPUPerformance()
      },
      memory: {
        total: memory.total,
        used: memory.used,
        available: memory.available,
        usage: (memory.used / memory.total) * 100,
        performance: this.analyzeMemoryPerformance(memory)
      },
      disk: {
        filesystems: disk.map(d => ({
          filesystem: d.fs,
          size: d.size,
          used: d.used,
          available: d.available,
          usage: d.use,
          mount: d.mount
        })),
        performance: this.analyzeDiskPerformance(disk)
      },
      network: {
        interfaces: network.map(n => ({
          iface: n.iface,
          rx_bytes: n.rx_bytes,
          tx_bytes: n.tx_bytes,
          rx_sec: n.rx_sec,
          tx_sec: n.tx_sec
        })),
        performance: this.analyzeNetworkPerformance(network)
      },
      processes: {
        total: processes.all,
        running: processes.running,
        sleeping: processes.sleeping,
        blocked: processes.blocked,
        topProcesses: processes.list
          .sort((a, b) => b.cpu - a.cpu)
          .slice(0, 10)
          .map(p => ({
            pid: p.pid,
            name: p.name,
            cpu: p.cpu,
            memory: p.mem
          }))
      }
    };

    return analysis;
  }

  async systemHealthCheck(parameters) {
    const checks = [];
    
    // CPU Health
    const cpu = await si.currentLoad();
    checks.push({
      component: 'CPU',
      status: cpu.currentLoad < this.alertThresholds.cpu ? 'healthy' : 'warning',
      value: cpu.currentLoad,
      threshold: this.alertThresholds.cpu,
      message: cpu.currentLoad < this.alertThresholds.cpu 
        ? 'CPU usage is normal' 
        : `High CPU usage: ${cpu.currentLoad.toFixed(1)}%`
    });

    // Memory Health
    const memory = await si.mem();
    const memoryUsage = (memory.used / memory.total) * 100;
    checks.push({
      component: 'Memory',
      status: memoryUsage < this.alertThresholds.memory ? 'healthy' : 'warning',
      value: memoryUsage,
      threshold: this.alertThresholds.memory,
      message: memoryUsage < this.alertThresholds.memory 
        ? 'Memory usage is normal' 
        : `High memory usage: ${memoryUsage.toFixed(1)}%`
    });

    // Disk Health
    const disks = await si.fsSize();
    for (const disk of disks) {
      checks.push({
        component: `Disk (${disk.mount})`,
        status: disk.use < this.alertThresholds.disk ? 'healthy' : 'critical',
        value: disk.use,
        threshold: this.alertThresholds.disk,
        message: disk.use < this.alertThresholds.disk 
          ? 'Disk usage is normal' 
          : `High disk usage: ${disk.use.toFixed(1)}%`
      });
    }

    // Temperature Health (if available)
    try {
      const temp = await si.cpuTemperature();
      if (temp.main !== null) {
        checks.push({
          component: 'Temperature',
          status: temp.main < this.alertThresholds.temperature ? 'healthy' : 'warning',
          value: temp.main,
          threshold: this.alertThresholds.temperature,
          message: temp.main < this.alertThresholds.temperature 
            ? 'Temperature is normal' 
            : `High temperature: ${temp.main}°C`
        });
      }
    } catch (error) {
      // Temperature monitoring not available
    }

    const healthyCount = checks.filter(c => c.status === 'healthy').length;
    const warningCount = checks.filter(c => c.status === 'warning').length;
    const criticalCount = checks.filter(c => c.status === 'critical').length;

    return {
      overallStatus: criticalCount > 0 ? 'critical' : warningCount > 0 ? 'warning' : 'healthy',
      summary: {
        total: checks.length,
        healthy: healthyCount,
        warning: warningCount,
        critical: criticalCount
      },
      checks,
      timestamp: new Date().toISOString()
    };
  }

  async resourceUsage(parameters) {
    const [cpu, memory, disk, network] = await Promise.all([
      si.currentLoad(),
      si.mem(),
      si.fsSize(),
      si.networkStats()
    ]);

    return {
      cpu: {
        usage: cpu.currentLoad,
        cores: cpu.cpus.map(core => ({
          load: core.load,
          loadUser: core.loadUser,
          loadSystem: core.loadSystem
        }))
      },
      memory: {
        total: memory.total,
        used: memory.used,
        free: memory.free,
        usage: (memory.used / memory.total) * 100,
        swap: {
          total: memory.swaptotal,
          used: memory.swapused,
          free: memory.swapfree
        }
      },
      disk: disk.map(d => ({
        filesystem: d.fs,
        mount: d.mount,
        total: d.size,
        used: d.used,
        available: d.available,
        usage: d.use
      })),
      network: network.map(n => ({
        interface: n.iface,
        bytesReceived: n.rx_bytes,
        bytesTransmitted: n.tx_bytes,
        packetsReceived: n.rx_packets,
        packetsTransmitted: n.tx_packets
      }))
    };
  }

  async processMonitor(parameters) {
    const { processName, duration = 60 } = parameters;
    
    if (!processName) {
      throw new ToolError('Process name is required for process monitoring');
    }

    const processes = await si.processes();
    const targetProcesses = processes.list.filter(p => 
      p.name.toLowerCase().includes(processName.toLowerCase())
    );

    if (targetProcesses.length === 0) {
      throw new ToolError(`No processes found matching: ${processName}`);
    }

    return {
      processName,
      matchingProcesses: targetProcesses.length,
      processes: targetProcesses.map(p => ({
        pid: p.pid,
        name: p.name,
        cpu: p.cpu,
        memory: p.mem,
        state: p.state,
        started: p.started,
        user: p.user,
        command: p.command
      })),
      summary: {
        totalCPU: targetProcesses.reduce((sum, p) => sum + p.cpu, 0),
        totalMemory: targetProcesses.reduce((sum, p) => sum + p.mem, 0),
        averageCPU: targetProcesses.reduce((sum, p) => sum + p.cpu, 0) / targetProcesses.length,
        averageMemory: targetProcesses.reduce((sum, p) => sum + p.mem, 0) / targetProcesses.length
      }
    };
  }

  generateMetricsSummary(metrics) {
    if (metrics.length === 0) return {};

    const cpuValues = metrics.map(m => m.cpu.usage);
    const memoryValues = metrics.map(m => m.memory.usage);

    return {
      cpu: {
        min: Math.min(...cpuValues),
        max: Math.max(...cpuValues),
        avg: cpuValues.reduce((sum, val) => sum + val, 0) / cpuValues.length,
        trend: this.calculateTrend(cpuValues)
      },
      memory: {
        min: Math.min(...memoryValues),
        max: Math.max(...memoryValues),
        avg: memoryValues.reduce((sum, val) => sum + val, 0) / memoryValues.length,
        trend: this.calculateTrend(memoryValues)
      }
    };
  }

  calculateTrend(values) {
    if (values.length < 2) return 'stable';
    
    const firstHalf = values.slice(0, Math.floor(values.length / 2));
    const secondHalf = values.slice(Math.floor(values.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;
    
    const change = ((secondAvg - firstAvg) / firstAvg) * 100;
    
    if (change > 5) return 'increasing';
    if (change < -5) return 'decreasing';
    return 'stable';
  }

  async analyzeCPUPerformance() {
    const load = await si.currentLoad();
    return {
      efficiency: load.currentLoad < 70 ? 'good' : load.currentLoad < 85 ? 'moderate' : 'poor',
      recommendation: load.currentLoad > 85 ? 'Consider reducing CPU-intensive tasks' : 'CPU performance is acceptable'
    };
  }

  analyzeMemoryPerformance(memory) {
    const usage = (memory.used / memory.total) * 100;
    return {
      efficiency: usage < 70 ? 'good' : usage < 85 ? 'moderate' : 'poor',
      recommendation: usage > 85 ? 'Consider freeing up memory or adding more RAM' : 'Memory usage is acceptable'
    };
  }

  analyzeDiskPerformance(disks) {
    const highUsage = disks.filter(d => d.use > 80);
    return {
      efficiency: highUsage.length === 0 ? 'good' : 'poor',
      recommendation: highUsage.length > 0 ? 'Clean up disk space or add more storage' : 'Disk usage is acceptable'
    };
  }

  analyzeNetworkPerformance(network) {
    return {
      efficiency: 'good', // Simplified analysis
      recommendation: 'Network performance monitoring requires baseline comparison'
    };
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  updateExecutionStats(executionTime) {
    if (!this.averageExecutionTime) {
      this.averageExecutionTime = executionTime;
    } else {
      this.averageExecutionTime = (this.averageExecutionTime + executionTime) / 2;
    }
  }

  async healthCheck() {
    try {
      // Test basic system information access
      await si.currentLoad();
      return true;
    } catch (error) {
      this.logger.error('Monitoring analytics health check failed:', error);
      return false;
    }
  }
}

module.exports = MonitoringAnalytics;
