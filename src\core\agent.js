const EventEmitter = require('events');
const LLMManager = require('./llm-manager');
const ContextManager = require('./context-manager');
const ExecutionEngine = require('./execution-engine');
const PluginManager = require('./plugin-manager');
const ToolRegistry = require('../tools');
const Logger = require('../utils/logger');
const { AgentError } = require('../utils/error-handler');

class Agent extends EventEmitter {
  constructor(config) {
    super();
    this.config = config;
    this.logger = new Logger('Agent');
    
    // Initialize core components
    this.llmManager = new LLMManager(config.llm);
    this.contextManager = new ContextManager(config.context);
    this.executionEngine = new ExecutionEngine(config.execution);
    this.pluginManager = new PluginManager(config.plugins);
    this.toolRegistry = new ToolRegistry();
    
    // Agent state
    this.isInitialized = false;
    this.currentTask = null;
    this.executionHistory = [];
    
    this.initialize();
  }

  async initialize() {
    try {
      this.logger.info('Initializing AI Agent...');
      
      // Initialize all components
      await this.llmManager.initialize();
      await this.contextManager.initialize();
      await this.executionEngine.initialize();
      await this.pluginManager.initialize();
      await this.toolRegistry.initialize();
      
      // Discover and index current environment
      await this.contextManager.discoverEnvironment();
      
      this.isInitialized = true;
      this.logger.info('AI Agent initialized successfully');
      this.emit('initialized');
      
    } catch (error) {
      this.logger.error('Failed to initialize agent:', error);
      throw new AgentError('Agent initialization failed', error);
    }
  }

  async processMessage(message, options = {}) {
    if (!this.isInitialized) {
      throw new AgentError('Agent not initialized');
    }

    try {
      this.logger.debug('Processing message:', message);
      
      // Update context with current environment
      await this.contextManager.updateContext();
      
      // Analyze the message and determine intent
      const analysis = await this.analyzeMessage(message);
      
      // If it's a simple question, respond directly
      if (analysis.type === 'question') {
        return await this.generateResponse(message, analysis);
      }
      
      // If it's a task, plan and execute
      if (analysis.type === 'task') {
        const plan = await this.planTask(message);
        
        // Ask for confirmation if needed
        if (analysis.requiresConfirmation && !options.autoConfirm) {
          this.emit('confirmationRequired', plan);
          return `I've analyzed your request and created an execution plan. Please confirm to proceed.`;
        }
        
        const result = await this.executeTask(plan, options);
        return this.formatExecutionResult(result);
      }
      
      // Default response
      return await this.generateResponse(message, analysis);
      
    } catch (error) {
      this.logger.error('Error processing message:', error);
      throw new AgentError('Failed to process message', error);
    }
  }

  async analyzeMessage(message) {
    const context = await this.contextManager.getContext();
    const availableTools = this.toolRegistry.getAvailableTools();

    const prompt = `
You are an autonomous AI agent analyzer. Analyze the following user message and determine:

1. Type: "question", "task", "conversation", "command"
2. Intent: What the user wants to accomplish
3. Complexity: "simple", "medium", "complex"
4. RequiresConfirmation: Whether this action needs user confirmation (boolean)
5. Tools: Array of tool names that might be needed
6. Risk: "low", "medium", "high" - based on potential system impact
7. Category: "file_ops", "system_info", "shell_command", "analysis", "general"
8. EstimatedSteps: Number of steps likely needed (1-10)
9. CanRunParallel: Whether parts can be executed in parallel (boolean)
10. RequiresContext: Whether current directory context is important (boolean)

Available Tools: ${availableTools.map(t => `${t.name}: ${t.description}`).join('\n')}

Current Context Summary:
- Platform: ${context.system.platform}
- Working Directory: ${context.workspace.path}
- Project Type: ${context.workspace.primaryType}
- Files Count: ${context.files.total}

User Message: "${message}"

Respond with ONLY a valid JSON object with the above fields. No additional text.`;

    try {
      const response = await this.llmManager.generateResponse(prompt, {
        maxTokens: 1000,
        temperature: 0.3
      });

      // Clean response and parse JSON
      const cleanResponse = response.replace(/```json\n?|\n?```/g, '').trim();
      const analysis = JSON.parse(cleanResponse);

      // Validate and set defaults
      return {
        type: analysis.type || 'conversation',
        intent: analysis.intent || 'General conversation',
        complexity: analysis.complexity || 'simple',
        requiresConfirmation: analysis.requiresConfirmation || false,
        tools: Array.isArray(analysis.tools) ? analysis.tools : [],
        risk: analysis.risk || 'low',
        category: analysis.category || 'general',
        estimatedSteps: analysis.estimatedSteps || 1,
        canRunParallel: analysis.canRunParallel || false,
        requiresContext: analysis.requiresContext || true,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      this.logger.warn('Failed to parse message analysis, using fallback:', error);

      // Fallback analysis
      return {
        type: message.includes('?') ? 'question' : 'task',
        intent: 'User request',
        complexity: 'simple',
        requiresConfirmation: false,
        tools: [],
        risk: 'low',
        category: 'general',
        estimatedSteps: 1,
        canRunParallel: false,
        requiresContext: true,
        timestamp: new Date().toISOString()
      };
    }
  }

  async planTask(task) {
    try {
      this.logger.info('Planning task:', task);

      // First analyze the task for better planning
      const analysis = await this.analyzeMessage(task);
      const context = await this.contextManager.getContext();
      const availableTools = this.toolRegistry.getAvailableTools();

      // Get tool schemas for better parameter planning
      const toolSchemas = availableTools.map(tool => ({
        name: tool.name,
        description: tool.description,
        schema: tool.schema,
        examples: tool.examples || []
      }));

      const prompt = `
You are an autonomous AI agent planner. Create a detailed, executable plan for the following task.

TASK: "${task}"

TASK ANALYSIS:
- Type: ${analysis.type}
- Complexity: ${analysis.complexity}
- Risk Level: ${analysis.risk}
- Estimated Steps: ${analysis.estimatedSteps}
- Can Run Parallel: ${analysis.canRunParallel}

CURRENT CONTEXT:
- Platform: ${context.system.platform}
- Working Directory: ${context.workspace.path}
- Project Type: ${context.workspace.primaryType || 'unknown'}
- Files Available: ${context.files.total}
- Recent History: ${context.history.slice(0, 3).map(h => h.type).join(', ')}

AVAILABLE TOOLS:
${toolSchemas.map(tool => `
${tool.name}:
  Description: ${tool.description}
  Required Parameters: ${tool.schema.required ? tool.schema.required.join(', ') : 'none'}
  Example: ${tool.examples[0] ? JSON.stringify(tool.examples[0].parameters) : 'N/A'}
`).join('\n')}

PLANNING REQUIREMENTS:
1. Break down the task into atomic, executable steps
2. Each step must use exactly one tool
3. Specify precise parameters for each tool call
4. Consider dependencies between steps
5. Identify steps that can run in parallel
6. Include error handling and validation steps
7. Ensure all file paths are relative to current directory: ${context.workspace.path}

OUTPUT FORMAT (JSON only, no additional text):
{
  "title": "Brief plan title",
  "description": "What this plan accomplishes",
  "steps": [
    {
      "id": "step_1",
      "action": "specific action to perform",
      "tool": "exact tool name from available tools",
      "parameters": {
        "param1": "exact value",
        "param2": "exact value"
      },
      "description": "Human readable description",
      "dependencies": ["step_id1", "step_id2"],
      "parallel": true/false,
      "validation": "how to verify this step succeeded",
      "errorHandling": "what to do if this step fails",
      "estimatedTime": 5000
    }
  ],
  "parallelGroups": [
    ["step_1", "step_2"],
    ["step_3"]
  ],
  "riskAssessment": "low/medium/high",
  "prerequisites": ["any requirements before starting"],
  "expectedOutcome": "what should result from this plan"
}`;

      const response = await this.llmManager.generateResponse(prompt, {
        maxTokens: 3000,
        temperature: 0.2
      });

      // Clean and parse response
      const cleanResponse = response.replace(/```json\n?|\n?```/g, '').trim();
      let plan;

      try {
        plan = JSON.parse(cleanResponse);
      } catch (parseError) {
        this.logger.warn('Failed to parse plan JSON, attempting to extract:', parseError);

        // Try to extract JSON from response
        const jsonMatch = cleanResponse.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          plan = JSON.parse(jsonMatch[0]);
        } else {
          throw new AgentError('Could not extract valid plan from LLM response');
        }
      }

      // Validate and enhance the plan
      plan = await this.validateAndEnhancePlan(plan, analysis, context);

      this.logger.debug('Generated plan:', plan);
      return plan;

    } catch (error) {
      this.logger.error('Failed to plan task:', error);
      throw new AgentError('Task planning failed', error);
    }
  }

  async validateAndEnhancePlan(plan, analysis, context) {
    // Add metadata
    plan.id = this.generatePlanId();
    plan.createdAt = new Date().toISOString();
    plan.taskAnalysis = analysis;
    plan.context = {
      workingDirectory: context.workspace.path,
      platform: context.system.platform,
      projectType: context.workspace.primaryType
    };

    // Validate steps
    if (!plan.steps || !Array.isArray(plan.steps)) {
      throw new AgentError('Plan must contain a steps array');
    }

    // Enhance each step
    plan.steps = plan.steps.map((step, index) => {
      // Ensure required fields
      step.id = step.id || `step_${index + 1}`;
      step.dependencies = step.dependencies || [];
      step.parallel = step.parallel !== undefined ? step.parallel : false;
      step.estimatedTime = step.estimatedTime || 5000;
      step.validation = step.validation || 'Check tool execution result';
      step.errorHandling = step.errorHandling || 'Log error and continue';

      // Validate tool exists
      if (!this.toolRegistry.hasTool(step.tool)) {
        throw new AgentError(`Unknown tool in plan: ${step.tool}`);
      }

      // Validate parameters against tool schema
      try {
        const tool = this.toolRegistry.getTool(step.tool);
        this.toolRegistry.validateParameters(tool, step.parameters || {});
      } catch (validationError) {
        this.logger.warn(`Parameter validation warning for step ${step.id}:`, validationError.message);
      }

      return step;
    });

    // Calculate total estimated duration
    plan.estimatedDuration = this.calculatePlanDuration(plan);

    // Validate dependencies
    this.validatePlanDependencies(plan);

    // Set execution strategy
    plan.executionStrategy = {
      parallel: analysis.canRunParallel && plan.steps.some(s => s.parallel),
      maxConcurrency: Math.min(3, Math.ceil(plan.steps.length / 2)),
      continueOnError: analysis.risk === 'low',
      timeout: Math.max(60000, plan.estimatedDuration * 2)
    };

    return plan;
  }

  validatePlanDependencies(plan) {
    const stepIds = new Set(plan.steps.map(s => s.id));

    for (const step of plan.steps) {
      for (const depId of step.dependencies) {
        if (!stepIds.has(depId)) {
          throw new AgentError(`Step ${step.id} depends on non-existent step: ${depId}`);
        }
      }
    }

    // Check for circular dependencies (simple check)
    const visited = new Set();
    const visiting = new Set();

    const hasCycle = (stepId) => {
      if (visiting.has(stepId)) return true;
      if (visited.has(stepId)) return false;

      visiting.add(stepId);
      const step = plan.steps.find(s => s.id === stepId);

      for (const depId of step.dependencies) {
        if (hasCycle(depId)) return true;
      }

      visiting.delete(stepId);
      visited.add(stepId);
      return false;
    };

    for (const step of plan.steps) {
      if (hasCycle(step.id)) {
        throw new AgentError(`Circular dependency detected involving step: ${step.id}`);
      }
    }
  }

  calculatePlanDuration(plan) {
    if (!plan.parallelGroups || plan.parallelGroups.length === 0) {
      // Sequential execution
      return plan.steps.reduce((total, step) => total + (step.estimatedTime || 5000), 0);
    }

    // Parallel execution - sum the longest path
    let totalTime = 0;
    for (const group of plan.parallelGroups) {
      const groupTime = Math.max(...group.map(stepId => {
        const step = plan.steps.find(s => s.id === stepId);
        return step ? (step.estimatedTime || 5000) : 5000;
      }));
      totalTime += groupTime;
    }

    return totalTime;
  }

  async executeTask(plan, options = {}) {
    try {
      this.logger.info('Executing task plan:', plan.id);
      this.currentTask = plan;

      // Merge plan execution strategy with options
      const executionOptions = {
        parallel: options.parallel !== undefined ? options.parallel : plan.executionStrategy?.parallel || false,
        dryRun: options.dryRun || false,
        maxConcurrency: options.maxConcurrency || plan.executionStrategy?.maxConcurrency || 3,
        timeout: options.timeout || plan.executionStrategy?.timeout || 300000,
        continueOnError: options.continueOnError !== undefined ? options.continueOnError : plan.executionStrategy?.continueOnError || false,
        ...options
      };

      // Pre-execution validation
      await this.preExecutionValidation(plan, executionOptions);

      // Set up progress tracking
      const progressTracker = this.createProgressTracker(plan);

      // Execute the plan with monitoring
      const result = await this.executeWithMonitoring(plan, executionOptions, progressTracker);

      // Post-execution analysis
      const enhancedResult = await this.postExecutionAnalysis(plan, result);

      // Update execution history
      this.executionHistory.push({
        plan,
        result: enhancedResult,
        timestamp: new Date().toISOString(),
        options: executionOptions
      });

      // Update context with results
      await this.contextManager.updateWithExecutionResult(enhancedResult);

      // Learn from execution for future planning
      await this.learnFromExecution(plan, enhancedResult);

      this.currentTask = null;
      this.logger.info('Task execution completed');

      return enhancedResult;

    } catch (error) {
      this.logger.error('Task execution failed:', error);
      this.currentTask = null;

      // Attempt error recovery if possible
      if (options.autoRecover !== false) {
        const recoveryResult = await this.attemptErrorRecovery(plan, error, options);
        if (recoveryResult) {
          return recoveryResult;
        }
      }

      throw new AgentError('Task execution failed', error);
    }
  }

  async preExecutionValidation(plan, options) {
    this.logger.debug('Performing pre-execution validation');

    // Check system resources
    const systemInfo = await this.contextManager.getSystemInfo();
    if (systemInfo.memory.usage > 90) {
      this.logger.warn('High memory usage detected, execution may be slower');
    }

    // Validate all tools are available
    for (const step of plan.steps) {
      if (!this.toolRegistry.hasTool(step.tool)) {
        throw new AgentError(`Required tool not available: ${step.tool}`);
      }
    }

    // Check for potential conflicts
    const fileOperations = plan.steps.filter(s => s.tool === 'file-operations');
    const conflictingOps = this.detectFileConflicts(fileOperations);
    if (conflictingOps.length > 0) {
      this.logger.warn('Potential file operation conflicts detected:', conflictingOps);
    }

    // Validate permissions for high-risk operations
    if (plan.riskAssessment === 'high') {
      await this.validateHighRiskOperations(plan);
    }
  }

  createProgressTracker(plan) {
    return {
      totalSteps: plan.steps.length,
      completedSteps: 0,
      failedSteps: 0,
      startTime: Date.now(),
      stepProgress: new Map(),

      updateStep: (stepId, status, result = null, error = null) => {
        this.stepProgress.set(stepId, { status, result, error, timestamp: Date.now() });

        if (status === 'completed') this.completedSteps++;
        if (status === 'failed') this.failedSteps++;

        // Emit progress event
        this.emit('executionProgress', {
          planId: plan.id,
          stepId,
          status,
          progress: (this.completedSteps / this.totalSteps) * 100,
          completedSteps: this.completedSteps,
          totalSteps: this.totalSteps,
          result,
          error
        });
      }
    };
  }

  async executeWithMonitoring(plan, options, progressTracker) {
    // Set up execution monitoring
    const executionMonitor = {
      startTime: Date.now(),
      memoryUsage: [],
      performanceMetrics: [],

      recordMetric: (metric) => {
        this.performanceMetrics.push({
          ...metric,
          timestamp: Date.now()
        });
      }
    };

    // Start monitoring interval
    const monitoringInterval = setInterval(() => {
      const memUsage = process.memoryUsage();
      executionMonitor.memoryUsage.push({
        timestamp: Date.now(),
        heapUsed: memUsage.heapUsed,
        heapTotal: memUsage.heapTotal,
        external: memUsage.external
      });
    }, 5000);

    try {
      // Execute with the execution engine
      const result = await this.executionEngine.execute(plan, {
        ...options,
        onStepStart: (stepInfo) => {
          progressTracker.updateStep(stepInfo.stepId, 'running');
          this.logger.debug(`Step started: ${stepInfo.stepId}`);
        },
        onStepComplete: (stepInfo) => {
          progressTracker.updateStep(stepInfo.stepId, 'completed', stepInfo.result);
          this.logger.debug(`Step completed: ${stepInfo.stepId}`);
        },
        onStepFailed: (stepInfo) => {
          progressTracker.updateStep(stepInfo.stepId, 'failed', null, stepInfo.error);
          this.logger.warn(`Step failed: ${stepInfo.stepId}`, stepInfo.error);
        }
      });

      // Add monitoring data to result
      result.monitoring = {
        executionTime: Date.now() - executionMonitor.startTime,
        memoryUsage: executionMonitor.memoryUsage,
        performanceMetrics: executionMonitor.performanceMetrics,
        progressTracker: progressTracker
      };

      return result;

    } finally {
      clearInterval(monitoringInterval);
    }
  }

  async postExecutionAnalysis(plan, result) {
    this.logger.debug('Performing post-execution analysis');

    // Analyze execution performance
    const performance = {
      plannedDuration: plan.estimatedDuration,
      actualDuration: result.duration,
      efficiency: plan.estimatedDuration / result.duration,
      successRate: (result.completedSteps / result.totalSteps) * 100
    };

    // Analyze step performance
    const stepAnalysis = plan.steps.map(step => {
      const stepResult = result.results.find(r => r.stepIndex === plan.steps.indexOf(step));
      return {
        stepId: step.id,
        planned: step.estimatedTime,
        actual: stepResult?.executionTime || 0,
        success: !!stepResult && !stepResult.error,
        tool: step.tool
      };
    });

    // Generate insights
    const insights = this.generateExecutionInsights(plan, result, performance, stepAnalysis);

    return {
      ...result,
      performance,
      stepAnalysis,
      insights,
      recommendations: this.generateRecommendations(insights)
    };
  }

  generateExecutionInsights(plan, result, performance, stepAnalysis) {
    const insights = [];

    // Performance insights
    if (performance.efficiency < 0.5) {
      insights.push({
        type: 'performance',
        level: 'warning',
        message: 'Execution took significantly longer than planned',
        suggestion: 'Consider breaking down complex steps or optimizing tool usage'
      });
    }

    if (performance.efficiency > 1.5) {
      insights.push({
        type: 'performance',
        level: 'positive',
        message: 'Execution completed faster than expected',
        suggestion: 'Planning estimates can be more aggressive for similar tasks'
      });
    }

    // Tool performance insights
    const toolPerformance = {};
    stepAnalysis.forEach(step => {
      if (!toolPerformance[step.tool]) {
        toolPerformance[step.tool] = { total: 0, successful: 0, avgTime: 0 };
      }
      toolPerformance[step.tool].total++;
      if (step.success) toolPerformance[step.tool].successful++;
      toolPerformance[step.tool].avgTime += step.actual;
    });

    Object.entries(toolPerformance).forEach(([tool, stats]) => {
      stats.avgTime /= stats.total;
      stats.successRate = (stats.successful / stats.total) * 100;

      if (stats.successRate < 80) {
        insights.push({
          type: 'tool',
          level: 'warning',
          message: `Tool ${tool} has low success rate: ${stats.successRate.toFixed(1)}%`,
          suggestion: 'Review tool configuration or parameter validation'
        });
      }
    });

    return insights;
  }

  generateRecommendations(insights) {
    const recommendations = [];

    // Group insights by type
    const warningInsights = insights.filter(i => i.level === 'warning');
    const positiveInsights = insights.filter(i => i.level === 'positive');

    if (warningInsights.length > 0) {
      recommendations.push({
        category: 'optimization',
        priority: 'high',
        action: 'Review and optimize execution strategy',
        details: warningInsights.map(i => i.suggestion)
      });
    }

    if (positiveInsights.length > 0) {
      recommendations.push({
        category: 'learning',
        priority: 'medium',
        action: 'Apply successful patterns to future tasks',
        details: positiveInsights.map(i => i.suggestion)
      });
    }

    return recommendations;
  }

  async generateResponse(message, analysis) {
    const context = await this.contextManager.getContext();
    
    const prompt = `
You are an autonomous AI assistant. Respond to the user's message based on the context.

Context: ${JSON.stringify(context, null, 2)}
Analysis: ${JSON.stringify(analysis, null, 2)}
Message: "${message}"

Provide a helpful, accurate response. If you need to perform actions, explain what you would do.`;

    return await this.llmManager.generateResponse(prompt);
  }

  async getSystemStatus() {
    return {
      provider: this.llmManager.getCurrentProvider(),
      model: this.llmManager.getCurrentModel(),
      contextSize: await this.contextManager.getContextSize(),
      availableTools: this.toolRegistry.getAvailableTools().length,
      healthy: await this.performHealthCheck(),
      currentTask: this.currentTask?.id || null,
      executionHistory: this.executionHistory.length
    };
  }

  async performHealthCheck() {
    try {
      // Check LLM connectivity
      await this.llmManager.healthCheck();
      
      // Check tool availability
      const toolsHealthy = await this.toolRegistry.healthCheck();
      
      // Check context manager
      const contextHealthy = await this.contextManager.healthCheck();
      
      return toolsHealthy && contextHealthy;
    } catch (error) {
      this.logger.error('Health check failed:', error);
      return false;
    }
  }

  generatePlanId() {
    return `plan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  estimatePlanDuration(plan) {
    // Simple estimation based on step count and complexity
    const baseTime = 5000; // 5 seconds base
    const stepTime = plan.steps.length * 2000; // 2 seconds per step
    return baseTime + stepTime;
  }

  formatExecutionResult(result) {
    if (result.success) {
      return `✅ Task completed successfully!\n\nSummary: ${result.summary}\nSteps completed: ${result.completedSteps}/${result.totalSteps}`;
    } else {
      return `❌ Task failed: ${result.error}\n\nCompleted: ${result.completedSteps}/${result.totalSteps} steps`;
    }
  }

  // Advanced autonomous capabilities
  async attemptErrorRecovery(plan, error, options) {
    this.logger.info('Attempting error recovery for plan:', plan.id);

    try {
      // Analyze the error
      const errorAnalysis = await this.analyzeExecutionError(error, plan);

      // Generate recovery strategies
      const recoveryStrategies = await this.generateRecoveryStrategies(errorAnalysis, plan);

      // Try each recovery strategy
      for (const strategy of recoveryStrategies) {
        try {
          this.logger.info(`Trying recovery strategy: ${strategy.name}`);
          const recoveryResult = await this.executeRecoveryStrategy(strategy, plan, options);

          if (recoveryResult.success) {
            this.logger.info('Error recovery successful');
            return recoveryResult;
          }
        } catch (recoveryError) {
          this.logger.warn(`Recovery strategy failed: ${strategy.name}`, recoveryError);
        }
      }

      this.logger.warn('All recovery strategies failed');
      return null;

    } catch (recoveryError) {
      this.logger.error('Error recovery process failed:', recoveryError);
      return null;
    }
  }

  async analyzeExecutionError(error, plan) {
    const prompt = `
Analyze the following execution error and provide recovery insights:

Error: ${error.message}
Error Type: ${error.constructor.name}
Plan ID: ${plan.id}
Plan Steps: ${plan.steps.length}

Provide analysis in JSON format with:
- errorCategory: "tool", "permission", "network", "resource", "logic", "unknown"
- severity: "low", "medium", "high", "critical"
- recoverable: boolean
- suggestedActions: array of recovery actions
- affectedSteps: array of step IDs that might be affected
- rootCause: likely root cause description
`;

    try {
      const response = await this.llmManager.generateResponse(prompt, { maxTokens: 800 });
      return JSON.parse(response.replace(/```json\n?|\n?```/g, '').trim());
    } catch (analysisError) {
      this.logger.warn('Error analysis failed, using fallback');
      return {
        errorCategory: 'unknown',
        severity: 'medium',
        recoverable: true,
        suggestedActions: ['retry', 'skip_failed_step'],
        affectedSteps: [],
        rootCause: 'Unable to analyze error automatically'
      };
    }
  }

  async generateRecoveryStrategies(errorAnalysis, plan) {
    const strategies = [];

    // Strategy 1: Retry with modified parameters
    if (errorAnalysis.recoverable && errorAnalysis.severity !== 'critical') {
      strategies.push({
        name: 'retry_with_modifications',
        description: 'Retry failed steps with modified parameters',
        priority: 1,
        action: 'retry',
        modifications: {
          timeout: 'increase',
          retries: 3,
          backoff: 'exponential'
        }
      });
    }

    // Strategy 2: Skip failed steps and continue
    if (errorAnalysis.severity === 'low' || errorAnalysis.severity === 'medium') {
      strategies.push({
        name: 'skip_and_continue',
        description: 'Skip failed steps and continue with remaining tasks',
        priority: 2,
        action: 'skip',
        continueOnError: true
      });
    }

    // Strategy 3: Alternative tool selection
    if (errorAnalysis.errorCategory === 'tool') {
      strategies.push({
        name: 'alternative_tools',
        description: 'Use alternative tools for failed operations',
        priority: 3,
        action: 'substitute',
        toolMappings: this.getAlternativeToolMappings()
      });
    }

    // Strategy 4: Simplified execution
    strategies.push({
      name: 'simplified_execution',
      description: 'Break down complex steps into simpler operations',
      priority: 4,
      action: 'simplify',
      maxStepComplexity: 'simple'
    });

    return strategies.sort((a, b) => a.priority - b.priority);
  }

  async executeRecoveryStrategy(strategy, originalPlan, options) {
    switch (strategy.action) {
      case 'retry':
        return await this.retryWithModifications(originalPlan, strategy, options);
      case 'skip':
        return await this.skipAndContinue(originalPlan, strategy, options);
      case 'substitute':
        return await this.substituteTools(originalPlan, strategy, options);
      case 'simplify':
        return await this.simplifyExecution(originalPlan, strategy, options);
      default:
        throw new Error(`Unknown recovery action: ${strategy.action}`);
    }
  }

  async retryWithModifications(plan, strategy, options) {
    const modifiedOptions = {
      ...options,
      timeout: options.timeout * 2,
      maxRetries: strategy.modifications.retries || 3,
      retryDelay: 2000
    };

    return await this.executionEngine.execute(plan, modifiedOptions);
  }

  async learnFromExecution(plan, result) {
    this.logger.debug('Learning from execution results');

    // Extract learning patterns
    const learningData = {
      planComplexity: plan.steps.length,
      executionTime: result.duration,
      successRate: result.performance.successRate,
      toolUsage: result.stepAnalysis.reduce((acc, step) => {
        acc[step.tool] = (acc[step.tool] || 0) + 1;
        return acc;
      }, {}),
      insights: result.insights,
      timestamp: new Date().toISOString()
    };

    // Store learning data for future planning improvements
    await this.contextManager.addLearningData(learningData);

    // Update tool performance metrics
    for (const stepAnalysis of result.stepAnalysis) {
      const tool = this.toolRegistry.getTool(stepAnalysis.tool);
      if (tool) {
        tool.updatePerformanceMetrics?.(stepAnalysis);
      }
    }
  }

  detectFileConflicts(fileOperations) {
    const conflicts = [];
    const pathAccess = new Map();

    fileOperations.forEach((op, index) => {
      const path = op.parameters.path;
      const operation = op.parameters.operation;

      if (!pathAccess.has(path)) {
        pathAccess.set(path, []);
      }

      pathAccess.get(path).push({ index, operation });
    });

    pathAccess.forEach((operations, path) => {
      if (operations.length > 1) {
        const hasWrite = operations.some(op => ['write', 'delete', 'move'].includes(op.operation));
        if (hasWrite) {
          conflicts.push({
            path,
            operations: operations.map(op => op.operation),
            risk: 'high'
          });
        }
      }
    });

    return conflicts;
  }

  async validateHighRiskOperations(plan) {
    const highRiskSteps = plan.steps.filter(step => {
      const params = step.parameters || {};

      // Check for dangerous shell commands
      if (step.tool === 'shell-commands') {
        const command = params.command || '';
        const dangerousPatterns = ['rm -rf', 'del /f', 'format', 'shutdown', 'reboot'];
        return dangerousPatterns.some(pattern => command.includes(pattern));
      }

      // Check for system file modifications
      if (step.tool === 'file-operations') {
        const path = params.path || '';
        const systemPaths = ['/etc', '/sys', '/proc', 'C:\\Windows', 'C:\\System32'];
        return systemPaths.some(sysPath => path.startsWith(sysPath));
      }

      return false;
    });

    if (highRiskSteps.length > 0) {
      this.logger.warn('High-risk operations detected:', highRiskSteps.map(s => s.id));
      // In a real implementation, this might require additional user confirmation
    }
  }

  getAlternativeToolMappings() {
    return {
      'shell-commands': ['file-operations'], // Use file ops instead of shell when possible
      'file-operations': ['shell-commands']  // Use shell as fallback for file ops
    };
  }

  // Event handlers for real-time updates
  onProgress(callback) {
    this.on('progress', callback);
  }

  onError(callback) {
    this.on('error', callback);
  }

  onComplete(callback) {
    this.on('complete', callback);
  }

  // Advanced agent capabilities
  async chainTasks(tasks, options = {}) {
    this.logger.info('Chaining multiple tasks:', tasks.length);

    const results = [];
    let context = await this.contextManager.getContext();

    for (let i = 0; i < tasks.length; i++) {
      const task = tasks[i];
      this.logger.info(`Executing chained task ${i + 1}/${tasks.length}: ${task}`);

      try {
        // Update context for each task
        await this.contextManager.updateContext();

        // Plan and execute task
        const plan = await this.planTask(task);
        const result = await this.executeTask(plan, options);

        results.push({
          task,
          plan,
          result,
          index: i
        });

        // Update context with results for next task
        context = await this.contextManager.getContext();

      } catch (error) {
        this.logger.error(`Chained task ${i + 1} failed:`, error);

        if (options.continueOnError) {
          results.push({
            task,
            error: error.message,
            index: i
          });
        } else {
          throw new AgentError(`Task chain failed at step ${i + 1}: ${error.message}`, error);
        }
      }
    }

    return {
      success: results.every(r => !r.error),
      results,
      totalTasks: tasks.length,
      completedTasks: results.filter(r => !r.error).length,
      summary: this.generateChainSummary(results)
    };
  }

  generateChainSummary(results) {
    const successful = results.filter(r => !r.error).length;
    const failed = results.filter(r => r.error).length;

    return `Task chain completed: ${successful} successful, ${failed} failed out of ${results.length} total tasks.`;
  }

  async optimizeForContext(task) {
    const context = await this.contextManager.getContext();
    const analysis = await this.analyzeMessage(task);

    // Optimize LLM provider selection
    const providerOptimization = await this.llmManager.optimizeProviderSelection(task, {
      streaming: analysis.complexity === 'complex',
      codeGeneration: context.workspace.primaryType === 'nodejs' || context.workspace.primaryType === 'python',
      functionCalling: analysis.tools.length > 0
    });

    if (providerOptimization.recommended !== this.llmManager.getCurrentProvider()) {
      this.logger.info(`Switching to optimized provider: ${providerOptimization.recommended}`);
      this.llmManager.switchProvider(providerOptimization.recommended);
    }

    return {
      provider: providerOptimization.recommended,
      reasoning: providerOptimization.reasoning,
      context: context.workspace.primaryType,
      analysis
    };
  }

  // Plugin management methods
  async loadPlugin(pluginPath) {
    return await this.pluginManager.loadPlugin(pluginPath);
  }

  async unloadPlugin(pluginName) {
    return await this.pluginManager.unloadPlugin(pluginName);
  }

  async executePlugin(pluginName, parameters = {}) {
    return await this.pluginManager.executePlugin(pluginName, parameters);
  }

  getAvailablePlugins() {
    return this.pluginManager.getAllPlugins();
  }

  async installPlugin(source) {
    return await this.pluginManager.installPlugin(source);
  }

  async createPlugin(name, template = 'basic') {
    return await this.pluginManager.createPlugin(name, template);
  }

  getPluginStats() {
    return this.pluginManager.getPluginStats();
  }

  // Enhanced system status with plugin information
  async getSystemStatus() {
    const [
      llmStatus,
      contextStatus,
      toolStatus,
      pluginStatus,
      executionStatus
    ] = await Promise.all([
      this.llmManager.getCurrentProvider(),
      this.contextManager.getContext(),
      this.toolRegistry.healthCheck(),
      this.pluginManager.healthCheck(),
      this.executionEngine.getStatus()
    ]);

    const healthyTools = Object.values(toolStatus).filter(h => h).length;
    const healthyPlugins = Object.values(pluginStatus).filter(h => h).length;

    return {
      provider: llmStatus,
      model: this.llmManager.providers.get(llmStatus)?.defaultModel || 'unknown',
      contextSize: contextStatus.history.length,
      availableTools: Object.keys(toolStatus).length,
      healthyTools,
      availablePlugins: Object.keys(pluginStatus).length,
      healthyPlugins,
      currentTask: this.currentTask?.id || null,
      executionHistory: this.executionHistory.length,
      healthy: healthyTools === Object.keys(toolStatus).length &&
               healthyPlugins === Object.keys(pluginStatus).length &&
               this.isInitialized,
      uptime: Date.now() - this.startTime,
      memoryUsage: process.memoryUsage()
    };
  }

  // Advanced health check including plugins
  async healthCheck() {
    try {
      const [
        llmHealth,
        contextHealth,
        toolHealth,
        pluginHealth,
        executionHealth
      ] = await Promise.all([
        this.llmManager.testProvider(),
        this.contextManager.healthCheck(),
        this.toolRegistry.healthCheck(),
        this.pluginManager.healthCheck(),
        this.executionEngine.healthCheck()
      ]);

      const overallHealth = llmHealth && contextHealth &&
                           Object.values(toolHealth).every(h => h) &&
                           Object.values(pluginHealth).every(h => h) &&
                           executionHealth;

      return {
        overall: overallHealth,
        components: {
          llm: llmHealth,
          context: contextHealth,
          tools: toolHealth,
          plugins: pluginHealth,
          execution: executionHealth
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('Health check failed:', error);
      return {
        overall: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

module.exports = Agent;
