#!/usr/bin/env node

// Basic test script to verify core functionality without LLM
const path = require('path');
const fs = require('fs-extra');

// Test imports
console.log('🧪 Testing AI CLI Agent Core Components...\n');

async function testComponents() {
  try {
    // Test Logger
    console.log('📝 Testing Logger...');
    const Logger = require('./src/utils/logger');
    const logger = new Logger('Test');
    logger.info('Logger test successful');
    console.log('✅ Logger working\n');

    // Test Config Manager
    console.log('⚙️ Testing Config Manager...');
    const ConfigManager = require('./src/config/config');
    const config = await ConfigManager.load();
    console.log('✅ Config Manager working\n');

    // Test Tool Registry
    console.log('🔧 Testing Tool Registry...');
    const ToolRegistry = require('./src/tools');
    const toolRegistry = new ToolRegistry();
    await toolRegistry.initialize();
    const tools = toolRegistry.getAvailableTools();
    console.log(`✅ Tool Registry working - ${tools.length} tools available\n`);

    // Test File Operations Tool
    console.log('📁 Testing File Operations...');
    const fileOps = toolRegistry.getTool('file-operations');
    if (fileOps) {
      // Test creating a temporary file
      const testFile = path.join(__dirname, 'test-temp.txt');
      const result = await fileOps.execute({
        operation: 'write',
        path: testFile,
        content: 'Hello AI CLI Agent!'
      });
      
      if (result.success) {
        console.log('✅ File write operation successful');
        
        // Clean up
        await fs.remove(testFile);
        console.log('✅ File cleanup successful\n');
      }
    }

    // Test Shell Commands Tool
    console.log('💻 Testing Shell Commands...');
    const shellTool = toolRegistry.getTool('shell-commands');
    if (shellTool) {
      const result = await shellTool.execute({
        command: process.platform === 'win32' ? 'echo Hello' : 'echo "Hello"',
        captureOutput: true
      });
      
      if (result.success && result.result.stdout.includes('Hello')) {
        console.log('✅ Shell command execution successful\n');
      }
    }

    // Test System Info Tool
    console.log('🖥️ Testing System Info...');
    const sysInfo = toolRegistry.getTool('system-info');
    if (sysInfo) {
      const result = await sysInfo.execute({
        category: 'basic'
      });
      
      if (result.success && result.result.platform) {
        console.log(`✅ System info successful - Platform: ${result.result.platform}\n`);
      }
    }

    // Test Context Manager
    console.log('🧠 Testing Context Manager...');
    const ContextManager = require('./src/core/context-manager');
    const contextManager = new ContextManager(config.context);
    await contextManager.initialize();
    await contextManager.discoverEnvironment();
    const context = await contextManager.getContext();
    console.log(`✅ Context Manager working - ${context.files.total} files indexed\n`);

    // Test Execution Engine
    console.log('⚡ Testing Execution Engine...');
    const ExecutionEngine = require('./src/core/execution-engine');
    const executionEngine = new ExecutionEngine(config.execution);
    await executionEngine.initialize();
    console.log('✅ Execution Engine working\n');

    console.log('🎉 All core components are working correctly!');
    console.log('\n📋 Summary:');
    console.log(`   • Platform: ${process.platform}`);
    console.log(`   • Node.js: ${process.version}`);
    console.log(`   • Tools Available: ${tools.length}`);
    console.log(`   • Files Indexed: ${context.files.total}`);
    console.log(`   • Project Type: ${context.workspace.primaryType || 'unknown'}`);
    
    console.log('\n🚀 Ready to use AI CLI Agent!');
    console.log('   • Configure API keys: node src/cli.js config --setup');
    console.log('   • Start chat: node src/cli.js chat');
    console.log('   • Execute task: node src/cli.js exec "your task here"');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

testComponents();
